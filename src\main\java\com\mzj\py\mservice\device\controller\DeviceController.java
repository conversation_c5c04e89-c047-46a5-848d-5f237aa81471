package com.mzj.py.mservice.device.controller;

import cn.hutool.core.bean.BeanUtil;
import com.mzj.py.aop.DevicePermission;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.device.service.DeviceService;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.Device;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 设备
 *
 * @author: duanjinze
 * @date: 2022/11/16 9:27
 * @version: 1.0
 */
@Controller
@RequestMapping("/mini/device")
public class DeviceController extends ApiBaseController {
    @Autowired
    private DeviceService deviceService;

    /**
     * 我的设备列表
     *
     * @param accessToken
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResultBean<Map<String, Object>> list(@RequestHeader String accessToken, @RequestBody DeviceQueryVo queryVo) {
        queryVo.setShopIds(getShopIds(accessToken));
        queryVo.setCreateId(getUser(accessToken).getId());
        return deviceService.list(queryVo);
    }

    /**
     * 解绑门店
     *
     * @return
     */
    @GetMapping("/unBind/{deviceId}")
    @ResponseBody
    public ResultBean<Boolean> unBind(@RequestHeader String accessToken, @PathVariable("deviceId") Long deviceId) {
        return deviceService.unBind(deviceId);
    }

    /**
     * 绑定门店
     *
     * @param vo
     * @return
     */
    @PostMapping("/bindShop")
    @ResponseBody
    public ResultBean<Boolean> bindShop(@RequestHeader String accessToken, @RequestBody DeviceUnBindVo vo) {
        return deviceService.bind(vo);
    }

    /**
     * 新增设备
     *
     * @param vo
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    public ResultBean<Boolean> add(@RequestHeader String accessToken, @RequestBody DeviceAddVo vo) {
        Device device = BeanUtil.toBean(vo, Device.class);
        if (vo.getShopId() == null) {
            device.setShopId(getShopId(accessToken));
        }
        device.setShopIds(getShopIds(accessToken));
        device.setUserId(getUser(accessToken).getId());
        return deviceService.addOrUpdate(device);
    }

    /**
     * 修改设备
     *
     * @param vo
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    @DevicePermission(key = "id")
    public ResultBean<Boolean> update(@RequestHeader String accessToken, @RequestBody DeviceUpdateVo vo) {
        Device device = BeanUtil.toBean(vo, Device.class);
        if (vo.getShopId() == null) {
            device.setShopId(getShopId(accessToken));
        }
        device.setUserId(getUser(accessToken).getId());
        return deviceService.addOrUpdate(device);
    }

    /**
     * 设备详情（展示设备与语音包关系）
     *
     * @param deviceId   设备id
     * @param pageSize   每页显示数
     * @param pageNumber 当前页
     * @return
     */
    @GetMapping("/detail")
    @ResponseBody
    public ResultBean<DetailVo> detail(
            @RequestParam(name = "deviceId") Long deviceId,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "pageNumber", defaultValue = "0") Integer pageNumber) {
        return deviceService.detail(deviceId, pageSize, pageNumber);
    }

    /**
     * 设备详情（展示设备与语音包关系） 包含设备字段音频
     *
     * @param deviceId 设备id
     * @return
     */
    @PostMapping("/detail/{deviceId}")
    @ResponseBody
    public ResultBean<DetailVo> detail(@PathVariable Long deviceId, @RequestBody List<DevVoiceVo> list) {
        return deviceService.deviceVoicedetail(deviceId, list);
    }

    /**
     * 语音包更新接口
     *
     * @param vo
     * @return
     */
    @PostMapping("/devicevoice")
    @ResponseBody
    public ResultBean<Boolean> updateDeviceVoice(@RequestBody DeviceVoiceVo vo) {
        return deviceService.updateDeviceVoice(vo);
    }

    /**
     * 删除语音包
     *
     * @param id
     * @return
     */
    @DeleteMapping
    @ResponseBody
    public ResultBean<Boolean> deleteDeviceVoice(@RequestParam(name = "id") Long id) {
        return deviceService.deleteDeviceVoice(id);
    }

    /**
     * 新增设备音频并发送音频到设备
     *
     * @return
     */
    // @PostMapping("/add/deviceVoice")
    // @ResponseBody
    // public ResultBean<Boolean> addDeviceVoice(@RequestHeader String accessToken,
    // @RequestBody DeviceVoiceAddParam addParam) {
    // Long shopId = super.getShopId(accessToken);
    // addParam.setShopId(shopId);
    // TokenRedisVo user = super.getUser(accessToken);
    // addParam.setUserId(user.getId());
    // return deviceService.addDeviceVoice(addParam);
    // }

    /**
     * 用户可选设备
     *
     * @return
     */
    @GetMapping("/tree/my")
    @ResponseBody
    public ResultBean<List<DeviceTreeOptionVo>> getTreeUserDeviceSelect(@RequestHeader String accessToken) {
        return deviceService.getTreeUserDeviceSelect(getShopIds(accessToken));
    }

    /**
     * 上传设备音频
     * @param accessToken
     * @param file
     * @param name
     * @return
     */
    @PostMapping("uploadDeviceVoice")
    @ResponseBody
    public ResultBean uploadDeviceVoice(@RequestHeader String accessToken, @RequestPart("file") MultipartFile file, String name){
        return deviceService.uploadDeviceVoice(accessToken,file,name);
    }
}
